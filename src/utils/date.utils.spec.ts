import {
  isYearFormat,
  convertYearToStartDate,
  convertYearToEndDate,
  processDateStart,
  processDateEnd,
  formatDateToYear,
  processDeliverableDates,
  formatDeliverableDatesForResponse,
} from './date.utils';

describe('Date Utils', () => {
  describe('isYearFormat', () => {
    it('should return true for valid YYYY format', () => {
      expect(isYearFormat('2024')).toBe(true);
      expect(isYearFormat('1999')).toBe(true);
      expect(isYearFormat('2100')).toBe(true);
    });

    it('should return false for invalid formats', () => {
      expect(isYearFormat('24')).toBe(false);
      expect(isYearFormat('202')).toBe(false);
      expect(isYearFormat('20244')).toBe(false);
      expect(isYearFormat('abc2024')).toBe(false);
      expect(isYearFormat('2024-01-01')).toBe(false);
      expect(isYearFormat(new Date())).toBe(false);
    });

    it('should return false for years outside valid range', () => {
      expect(isYearFormat('1899')).toBe(false);
      expect(isYearFormat('2101')).toBe(false);
    });
  });

  describe('convertYearToStartDate', () => {
    it('should convert YYYY to January 1st', () => {
      const result = convertYearToStartDate('2024');
      expect(result.getFullYear()).toBe(2024);
      expect(result.getMonth()).toBe(0); // January
      expect(result.getDate()).toBe(1);
    });

    it('should throw error for invalid year format', () => {
      expect(() => convertYearToStartDate('invalid')).toThrow('Invalid year format: invalid');
    });
  });

  describe('convertYearToEndDate', () => {
    it('should convert YYYY to December 31st', () => {
      const result = convertYearToEndDate('2024');
      expect(result.getFullYear()).toBe(2024);
      expect(result.getMonth()).toBe(11); // December
      expect(result.getDate()).toBe(31);
      expect(result.getHours()).toBe(23);
      expect(result.getMinutes()).toBe(59);
      expect(result.getSeconds()).toBe(59);
    });

    it('should throw error for invalid year format', () => {
      expect(() => convertYearToEndDate('invalid')).toThrow('Invalid year format: invalid');
    });
  });

  describe('processDateStart', () => {
    it('should process YYYY format to January 1st', () => {
      const result = processDateStart('2024');
      expect(result.getFullYear()).toBe(2024);
      expect(result.getMonth()).toBe(0);
      expect(result.getDate()).toBe(1);
    });

    it('should return Date object as-is', () => {
      const date = new Date('2024-06-15');
      const result = processDateStart(date);
      expect(result).toBe(date);
    });

    it('should parse ISO date string', () => {
      const result = processDateStart('2024-06-15T10:30:00.000Z');
      expect(result.getFullYear()).toBe(2024);
      expect(result.getMonth()).toBe(5); // June
      expect(result.getDate()).toBe(15);
    });

    it('should throw error for invalid input', () => {
      expect(() => processDateStart('invalid-date')).toThrow('Invalid date format: invalid-date');
    });
  });

  describe('processDateEnd', () => {
    it('should auto-generate December 31st when dateStart is YYYY and dateEnd is undefined', () => {
      const result = processDateEnd(undefined, '2024');
      expect(result?.getFullYear()).toBe(2024);
      expect(result?.getMonth()).toBe(11); // December
      expect(result?.getDate()).toBe(31);
    });

    it('should process explicit dateEnd in YYYY format', () => {
      const result = processDateEnd('2025', '2024');
      expect(result?.getFullYear()).toBe(2025);
      expect(result?.getMonth()).toBe(11); // December
      expect(result?.getDate()).toBe(31);
    });

    it('should return Date object as-is', () => {
      const date = new Date('2024-12-31');
      const result = processDateEnd(date, '2024');
      expect(result).toBe(date);
    });

    it('should return undefined when dateEnd is not provided and dateStart is not YYYY', () => {
      const result = processDateEnd(undefined, new Date('2024-06-15'));
      expect(result).toBeUndefined();
    });
  });

  describe('formatDateToYear', () => {
    it('should format Date to YYYY string', () => {
      const date = new Date('2024-06-15T10:30:00.000Z');
      const result = formatDateToYear(date);
      expect(result).toBe('2024');
    });

    it('should throw error for invalid date', () => {
      expect(() => formatDateToYear(new Date('invalid'))).toThrow('Invalid date provided for formatting');
    });
  });

  describe('processDeliverableDates', () => {
    it('should process YYYY dateStart and auto-generate dateEnd', () => {
      const result = processDeliverableDates('2024');
      expect(result.dateStart.getFullYear()).toBe(2024);
      expect(result.dateStart.getMonth()).toBe(0); // January
      expect(result.dateEnd?.getFullYear()).toBe(2024);
      expect(result.dateEnd?.getMonth()).toBe(11); // December
    });

    it('should process both YYYY dates', () => {
      const result = processDeliverableDates('2024', '2025');
      expect(result.dateStart.getFullYear()).toBe(2024);
      expect(result.dateEnd?.getFullYear()).toBe(2025);
    });

    it('should process Date objects', () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');
      const result = processDeliverableDates(startDate, endDate);
      expect(result.dateStart).toBe(startDate);
      expect(result.dateEnd).toBe(endDate);
    });
  });

  describe('formatDeliverableDatesForResponse', () => {
    it('should format both dates to YYYY strings', () => {
      const startDate = new Date(2024, 0, 1); // January 1, 2024
      const endDate = new Date(2024, 11, 31); // December 31, 2024
      const result = formatDeliverableDatesForResponse(startDate, endDate);
      expect(result.dateStart).toBe('2024');
      expect(result.dateEnd).toBe('2024');
    });

    it('should handle different years', () => {
      const startDate = new Date(2024, 0, 1); // January 1, 2024
      const endDate = new Date(2025, 11, 31); // December 31, 2025
      const result = formatDeliverableDatesForResponse(startDate, endDate);
      expect(result.dateStart).toBe('2024');
      expect(result.dateEnd).toBe('2025');
    });
  });
});
